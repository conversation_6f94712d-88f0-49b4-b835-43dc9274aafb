import { Pool } from 'pg';
import { Product, Feature, ProductFormData, FeatureFormData } from './types';
import { creem } from './creem';

class Database {
  private pool: Pool;

  constructor() {
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is not set');
    }

    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL,
    });
  }

  async query<T = any>(text: string, params?: any[]): Promise<T[]> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(text, params);
      return result.rows;
    } finally {
      client.release();
    }
  }

  // Product operations
  async getProducts(): Promise<Product[]> {
    const query = `
      SELECT p.*, 
             COALESCE(
               json_agg(
                 json_build_object(
                   'id', f.id,
                   'name', f.name,
                   'createdAt', f."createdAt",
                   'updatedAt', f."updatedAt"
                 )
               ) FILTER (WHERE f.id IS NOT NULL), 
               '[]'
             ) as features
      FROM "Product" p
      LEFT JOIN "_FeatureToProduct" fp ON p.id = fp."A"
      LEFT JOIN "Feature" f ON fp."B" = f.id
      GROUP BY p.id
      ORDER BY p.order ASC, p."createdAt" DESC
    `;
    return this.query<Product>(query);
  }

  async getProductById(id: string): Promise<Product | null> {
    const query = `
      SELECT p.*, 
             COALESCE(
               json_agg(
                 json_build_object(
                   'id', f.id,
                   'name', f.name,
                   'createdAt', f."createdAt",
                   'updatedAt', f."updatedAt"
                 )
               ) FILTER (WHERE f.id IS NOT NULL), 
               '[]'
             ) as features
      FROM "Product" p
      LEFT JOIN "_FeatureToProduct" fp ON p.id = fp."A"
      LEFT JOIN "Feature" f ON fp."B" = f.id
      WHERE p.id = $1
      GROUP BY p.id
    `;
    const results = await this.query<Product>(query, [id]);
    return results[0] || null;
  }

  async createProduct(data: ProductFormData): Promise<Product> {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');

      // Generate cuid-like ID
      const id = `c${Date.now()}${Math.random().toString(36).substr(2, 9)}`;

      let creemPlanId = data.planId;

      // Only create in Creem if provider is CREEM
      if (data.providerName === 'CREEM') {
        try {
          const creemProduct = await creem.createProduct({
            name: data.name,
            description: data.description,
            price: data.price,
            interval: data.interval,
          });
          creemPlanId = creemProduct.id;
        } catch (error) {
          console.error('Failed to create product in Creem:', error);
          await client.query('ROLLBACK');
          throw new Error(`Failed to create product in Creem: ${error}`);
        }
      }

      const productQuery = `
        INSERT INTO "Product" (
          id, "planId", "providerName", name, description, price, credits, status, interval, "createdAt", "updatedAt"
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
        RETURNING *
      `;

      const productResult = await client.query(productQuery, [
        id,
        creemPlanId,
        data.providerName,
        data.name,
        data.description,
        data.price,
        data.credits,
        data.status,
        data.interval,
      ]);

      // Link features
      if (data.featureIds.length > 0) {
        // First, validate that all feature IDs exist
        const featureCheckQuery = `
          SELECT id FROM "Feature" WHERE id = ANY($1)
        `;
        const existingFeatures = await client.query(featureCheckQuery, [
          data.featureIds,
        ]);

        if (existingFeatures.rows.length !== data.featureIds.length) {
          const existingIds = existingFeatures.rows.map((row) => row.id);
          const missingIds = data.featureIds.filter(
            (id) => !existingIds.includes(id)
          );
          throw new Error(`Feature IDs not found: ${missingIds.join(', ')}`);
        }

        const featureValues = data.featureIds
          .map((featureId, index) => `($${index * 2 + 1}, $${index * 2 + 2})`)
          .join(', ');

        const featureParams = data.featureIds.flatMap((featureId) => [
          id,
          featureId,
        ]);

        await client.query(
          `INSERT INTO "_FeatureToProduct" ("A", "B") VALUES ${featureValues}`,
          featureParams
        );
      }

      await client.query('COMMIT');
      return productResult.rows[0];
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  async updateProduct(id: string, data: ProductFormData): Promise<Product> {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');

      // Get current product to check if it's a Creem product
      const currentProduct = await this.getProductById(id);

      const productQuery = `
        UPDATE "Product" 
        SET "planId" = $2, "providerName" = $3, name = $4, description = $5, 
            price = $6, credits = $7, status = $8, interval = $9, "updatedAt" = NOW()
        WHERE id = $1
        RETURNING *
      `;

      const productResult = await client.query(productQuery, [
        id,
        data.planId,
        data.providerName,
        data.name,
        data.description,
        data.price,
        data.credits,
        data.status,
        data.interval,
      ]);

      // Update in Creem if it's a Creem product
      if (currentProduct && currentProduct.providerName === 'CREEM') {
        try {
          await creem.updateProduct(currentProduct.planId, {
            name: data.name,
            description: data.description,
            price: data.price,
          });
        } catch (error) {
          console.error('Failed to update product in Creem:', error);
          // Don't fail the entire operation if Creem update fails
        }
      }

      // Remove existing feature links
      await client.query('DELETE FROM "_FeatureToProduct" WHERE "A" = $1', [
        id,
      ]);

      // Add new feature links
      if (data.featureIds.length > 0) {
        // First, validate that all feature IDs exist
        const featureCheckQuery = `
          SELECT id FROM "Feature" WHERE id = ANY($1)
        `;
        const existingFeatures = await client.query(featureCheckQuery, [
          data.featureIds,
        ]);

        if (existingFeatures.rows.length !== data.featureIds.length) {
          const existingIds = existingFeatures.rows.map((row) => row.id);
          const missingIds = data.featureIds.filter(
            (id) => !existingIds.includes(id)
          );
          throw new Error(`Feature IDs not found: ${missingIds.join(', ')}`);
        }

        const featureValues = data.featureIds
          .map((featureId, index) => `($${index * 2 + 1}, $${index * 2 + 2})`)
          .join(', ');

        const featureParams = data.featureIds.flatMap((featureId) => [
          id,
          featureId,
        ]);

        await client.query(
          `INSERT INTO "_FeatureToProduct" ("A", "B") VALUES ${featureValues}`,
          featureParams
        );
      }

      await client.query('COMMIT');
      return productResult.rows[0];
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  async deleteProduct(id: string): Promise<void> {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');

      // Get product details before deletion
      const product = await this.getProductById(id);

      // Archive in Creem if it's a Creem product
      if (product && product.providerName === 'CREEM') {
        try {
          await creem.archiveProduct(product.planId);
        } catch (error) {
          console.error('Failed to archive product in Creem:', error);
          // Don't fail the entire operation if Creem archive fails
        }
      }

      // Remove feature links first
      await client.query('DELETE FROM "_FeatureToProduct" WHERE "A" = $1', [
        id,
      ]);

      // Delete the product
      await client.query('DELETE FROM "Product" WHERE id = $1', [id]);

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Feature operations
  async getFeatures(): Promise<Feature[]> {
    const query = `
      SELECT f.*,
             COALESCE(
               json_agg(
                 json_build_object(
                   'id', p.id,
                   'name', p.name,
                   'planId', p."planId",
                   'providerName', p."providerName",
                   'price', p.price,
                   'status', p.status
                 )
               ) FILTER (WHERE p.id IS NOT NULL), 
               '[]'
             ) as products
      FROM "Feature" f
      LEFT JOIN "_FeatureToProduct" fp ON f.id = fp."B"
      LEFT JOIN "Product" p ON fp."A" = p.id
      GROUP BY f.id
      ORDER BY f.name ASC
    `;
    return this.query<Feature>(query);
  }

  async getFeatureById(id: string): Promise<Feature | null> {
    const query = `
      SELECT f.*,
             COALESCE(
               json_agg(
                 json_build_object(
                   'id', p.id,
                   'name', p.name,
                   'planId', p."planId",
                   'providerName', p."providerName",
                   'price', p.price,
                   'status', p.status
                 )
               ) FILTER (WHERE p.id IS NOT NULL), 
               '[]'
             ) as products
      FROM "Feature" f
      LEFT JOIN "_FeatureToProduct" fp ON f.id = fp."B"
      LEFT JOIN "Product" p ON fp."A" = p.id
      WHERE f.id = $1
      GROUP BY f.id
    `;
    const results = await this.query<Feature>(query, [id]);
    return results[0] || null;
  }

  async createFeature(data: FeatureFormData): Promise<Feature> {
    const id = `c${Date.now()}${Math.random().toString(36).substr(2, 9)}`;
    const query = `
      INSERT INTO "Feature" (id, name, "createdAt", "updatedAt")
      VALUES ($1, $2, NOW(), NOW())
      RETURNING *
    `;
    const results = await this.query<Feature>(query, [id, data.name]);
    return results[0];
  }

  async updateFeature(id: string, data: FeatureFormData): Promise<Feature> {
    const query = `
      UPDATE "Feature" 
      SET name = $2, "updatedAt" = NOW()
      WHERE id = $1
      RETURNING *
    `;
    const results = await this.query<Feature>(query, [id, data.name]);
    return results[0];
  }

  async deleteFeature(id: string): Promise<void> {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');

      // Remove product links first
      await client.query('DELETE FROM "_FeatureToProduct" WHERE "B" = $1', [
        id,
      ]);

      // Delete the feature
      await client.query('DELETE FROM "Feature" WHERE id = $1', [id]);

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }
}

export const db = new Database();
